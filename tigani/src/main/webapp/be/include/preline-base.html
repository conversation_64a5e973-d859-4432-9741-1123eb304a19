<!DOCTYPE html>
<html lang="it" dir="ltr" class="relative min-h-full">
    <head>
        <!-- Meta -->
        <meta charset="utf-8">        
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">                       
        
        <!-- Favicon -->
        <link rel="shortcut icon" href="../favicon.ico">

        <!-- Font -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">                       
        
        <!-- CSS -->
        <link href="{{ contextPath }}/default/css/main.min.css" rel="stylesheet" type="text/css">           
       
        <!-- Theme Check and Update -->
        <script>
          const html = document.querySelector('html');
          const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
          const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

          if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
          else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
          else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
          else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
        </script>
       
        {% block extrahead %}{% endblock %}
    </head>

    <body class="bg-gray-50 dark:bg-neutral-900">
        
        <!-- ========== HEADER ========== -->
        {% include "be/include/snippets/preline-header.html" %}
        <!-- ========== END HEADER ========== -->

        <!-- ========== MAIN CONTENT ========== -->
        <main id="content" class="pt-15">
          <!-- Container -->
          <div class="max-w-full mx-auto">            
            <!-- Content -->
            
                <!-- ========== MAIN SIDEBAR ========== -->
                {% include "be/include/snippets/preline-sidebar.html" %}
                <!-- ========== END MAIN SIDEBAR ========== -->
                
                {% block content %}{% endblock %}
            
        </main>        

        
        <!-- JS VENDOR -->
        <script src="{{ contextPath }}/default/js/lodash.min.js"></script> 
        <script src="{{ contextPath }}/default/js/vanilla-calendar-pro.min.js"></script>
        <!-- JS -->
        <script src="{{ contextPath }}/default/js/preline.min.js"></script>
    </body>

</html>
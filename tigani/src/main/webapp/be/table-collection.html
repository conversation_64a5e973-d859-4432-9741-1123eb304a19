{% extends "be/include/preline-base.html" %}

{% block extrahead %}
    <title>Manutenzione / Tabelle</title>

    <!-- Jquery -->
    <script src="{{ contextPath }}/default/js/jquery.min.js"></script>
    
    <!-- Datatable -->
    <script src="{{ contextPath }}/default/js/dataTables.min.js"></script>

    <!-- Date Range Picker -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

{% endblock %}

{% block content %}
<div class="lg:ps-65 p-2 sm:p-5 md:pt-5 space-y-5">
    <!-- Card con DataTable -->
<div class="bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-900 dark:border-neutral-700">
    <!-- Header -->
    <div class="py-3 px-5 flex flex-wrap justify-between items-center gap-2 border-b border-gray-200 dark:border-neutral-700">
        <div class="flex flex-wrap items-center gap-2">
            <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                Tabella
            </h2>
        </div>
        <div class="flex flex-wrap items-center gap-2">
            <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-hidden focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                <svg class="hidden sm:block shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                Nuova tabella
            </button>
        </div>
    </div>
    <!-- End Header -->
    
    <!-- DataTable Content -->
    <div class="p-5">
        <div class="flex flex-col">
            <div id="user-datatable-container">
                <!-- Search Bar -->
                <div class="py-3">
                    <div class="relative max-w-xs w-full">
                        <label for="hs-table-input-search" class="sr-only">Cerca</label>
                        <input type="text" name="hs-table-search" id="hs-table-input-search" class="py-1.5 sm:py-2 px-3 ps-9 block w-full border-gray-200 shadow-2xs rounded-lg sm:text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Cerca elementi" data-hs-datatable-search="">
                        <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
                            <svg class="size-4 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.3-4.3"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <!-- Table Container -->
                <div class="min-h-[521px] overflow-x-auto">
                    <div class="min-w-full inline-block align-middle">
                        <div class="overflow-hidden">
                            <table class="min-w-full datatable">
                                <thead class="border-y border-gray-200 dark:border-neutral-700">
                                    <tr>
                                        <th scope="col" class="py-1 px-3 pe-0 --exclude-from-ordering">
                                            <div class="flex items-center h-5">
                                                <input id="hs-table-search-checkbox-all" type="checkbox" class="border-gray-300 rounded-sm text-blue-600 checked:border-blue-500 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800">
                                                <label for="hs-table-search-checkbox-all" class="sr-only">Checkbox</label>
                                            </div>
                                        </th>
                                        <th scope="col" class="py-1 group text-start font-normal focus:outline-hidden">
                                            <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm text-gray-500 rounded-md hover:border-gray-200 dark:text-neutral-500 dark:hover:border-neutral-700">
                                                Nome
                                                <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                    <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                                </svg>
                                            </div>
                                        </th>
                                        <th scope="col" class="py-1 group text-start font-normal focus:outline-hidden">
                                            <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm text-gray-500 rounded-md hover:border-gray-200 dark:text-neutral-500 dark:hover:border-neutral-700">
                                                Email
                                                <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                    <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                                </svg>
                                            </div>
                                        </th>
                                        <th scope="col" class="py-1 group text-start font-normal focus:outline-hidden">
                                            <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm text-gray-500 rounded-md hover:border-gray-200 dark:text-neutral-500 dark:hover:border-neutral-700">
                                                Telefono
                                                <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                    <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                                </svg>
                                            </div>
                                        </th>
                                        <th scope="col" class="py-1 group text-start font-normal focus:outline-hidden">
                                            <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm text-gray-500 rounded-md hover:border-gray-200 dark:text-neutral-500 dark:hover:border-neutral-700">
                                                Profilo
                                                <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                    <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                                </svg>
                                            </div>
                                        </th>
                                        <th scope="col" class="py-1 group text-start font-normal focus:outline-hidden">
                                            <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm text-gray-500 rounded-md hover:border-gray-200 dark:text-neutral-500 dark:hover:border-neutral-700">
                                                Data Creazione
                                                <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                    <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                                </svg>
                                            </div>
                                        </th>
                                        <th scope="col" class="py-1 group text-start font-normal focus:outline-hidden">
                                            <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm text-gray-500 rounded-md hover:border-gray-200 dark:text-neutral-500 dark:hover:border-neutral-700">
                                                Data Modifica
                                                <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                    <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                                </svg>
                                            </div>
                                        </th>
                                        <th scope="col" class="py-2 px-3 text-end font-normal text-sm text-gray-500 --exclude-from-ordering dark:text-neutral-500">Azioni</th>
                                        <th scope="col" class="py-2 px-3 text-center font-normal text-sm text-gray-500 --exclude-from-ordering dark:text-neutral-500"></th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 dark:divide-neutral-700">
                                    <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Pagination -->
                <div class="py-1 px-4" data-hs-datatable-paging="">
                    <nav class="flex items-center space-x-1">
                        <button type="button" class="p-2.5 min-w-10 inline-flex justify-center items-center gap-x-2 text-sm rounded-full text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-datatable-paging-prev="">
                            <span aria-hidden="true">«</span>
                            <span class="sr-only">Precedente</span>
                        </button>
                        <div class="flex items-center space-x-1 [&>.active]:bg-gray-100 dark:[&>.active]:bg-neutral-700" data-hs-datatable-paging-pages=""></div>
                        <button type="button" class="p-2.5 min-w-10 inline-flex justify-center items-center gap-x-2 text-sm rounded-full text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-datatable-paging-next="">
                            <span class="sr-only">Successivo</span>
                            <span aria-hidden="true">»</span>
                        </button>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <!-- End DataTable Content -->
</div>
<!-- End Card -->
</div>
<!-- End Container -->

<!-- Table Filters Offcanvas -->
<div id="table-filters" class="hs-overlay hs-overlay-open:translate-x-0 hidden translate-x-full fixed top-0 end-0 transition-all duration-300 transform size-full sm:w-100 z-80 flex flex-col bg-white dark:bg-neutral-800" role="dialog" tabindex="-1" aria-labelledby="hs-pro-shflo-label">
    <!-- Header -->
    <div class="py-3 px-6 flex justify-between items-center border-b border-gray-200 dark:border-neutral-700">
        <h3 id="hs-pro-shflo-label" class="font-medium text-gray-800 dark:text-neutral-200">
            Filtra
        </h3>
        <button type="button" class="py-1.5 px-2 inline-flex justify-center items-center gap-x-1 rounded-full border border-gray-200 text-xs text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:hover:bg-neutral-700 dark:text-neutral-200 dark:focus:bg-neutral-700" aria-label="Close" data-hs-overlay="#table-filters">
            <span class="hidden lg:block">Esc</span>
            <span class="block lg:hidden">Chiudi</span>
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
        </button>
    </div>
    <!-- End Header -->

    <!-- Body -->
    <div class="bg-gray-100 h-full overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-800">
        <div class="p-2 space-y-2">
            <!-- Card -->
            <div class="p-4 bg-white rounded-lg shadow-2xs dark:bg-neutral-900">
                <div class="mb-3">
                    <span class="font-medium text-sm text-gray-800 dark:text-neutral-200">Filtri applicati</span>
                </div>

                <!-- Item -->
                <span id="hs-pro-shfdaf1" class="hs-removing:opacity-0 transition duration-300 p-1 ps-2 inline-flex items-center gap-1.5 bg-gray-100 text-gray-800 text-sm rounded-md dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200">
                    Filtro
                    <button type="button" class="inline-flex shrink-0 justify-center items-center size-5 rounded-md text-gray-600 hover:bg-gray-200 focus:outline-hidden focus:bg-gray-200 text-sm dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:text-neutral-400" data-hs-remove-element="#hs-pro-shfdaf1">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg>
                    </button>
                </span>
                <!-- End Item -->

                <!-- Item -->
                <span id="hs-pro-shfdaf2" class="hs-removing:opacity-0 transition duration-300 p-1 ps-2 inline-flex items-center gap-1.5 bg-gray-100 text-gray-800 text-sm rounded-md dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200">
                    Filtro
                    <button type="button" class="inline-flex shrink-0 justify-center items-center size-5 rounded-md text-gray-600 hover:bg-gray-200 focus:outline-hidden focus:bg-gray-200 text-sm dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:text-neutral-400" data-hs-remove-element="#hs-pro-shfdaf2">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">  <path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg>
                    </button>
                </span>
                <!-- End Item -->
            </div>
            <!-- End Card -->

            <!-- Card -->
            <div class="p-4 bg-white rounded-lg shadow-2xs dark:bg-neutral-900">
                <div class="mb-3">
                    <span class="font-medium text-sm text-gray-800 dark:text-neutral-200">Gruppo di filtri</span>
                </div>

                <!-- List -->
                <div class="space-y-0.5">
                    <!-- Checkbox -->
                    <div class="flex items-center">
                        <label for="hs-pro-shfloc-shirts" class="p-2 group w-full inline-flex items-center cursor-pointer text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                            <input type="checkbox" class="shrink-0 size-4.5 border-gray-300 rounded-sm text-blue-600 checked:border-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-500 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" id="hs-pro-shfloc-shirts">
                                <span class="ms-2 text-gray-800 dark:text-neutral-400">Filtro</span>
                                <span class="ms-auto text-xs text-gray-500 dark:text-neutral-500">(47)</span>
                        </label>
                    </div>
                    <!-- End Checkbox -->

                    <!-- Checkbox -->
                    <div class="flex items-center">
                        <label for="hs-pro-shfloc-tshirts" class="p-2 group w-full inline-flex items-center cursor-pointer text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                            <input type="checkbox" class="shrink-0 size-4.5 border-gray-300 rounded-sm text-blue-600 checked:border-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-500 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" id="hs-pro-shfloc-tshirts">
                                <span class="ms-2 text-gray-800 dark:text-neutral-400">Filtro</span>
                                <span class="ms-auto text-xs text-gray-500 dark:text-neutral-500">(89)</span>
                        </label>
                    </div>
                    <!-- End Checkbox -->

                    <!-- Checkbox -->
                    <div class="flex items-center">
                        <label for="hs-pro-shfloc-polos" class="p-2 group w-full inline-flex items-center cursor-pointer text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                            <input type="checkbox" class="shrink-0 size-4.5 border-gray-300 rounded-sm text-blue-600 checked:border-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-500 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" id="hs-pro-shfloc-polos">
                                <span class="ms-2 text-gray-800 dark:text-neutral-400">Filtro</span>
                                <span class="ms-auto text-xs text-gray-500 dark:text-neutral-500">(35)</span>
                        </label>
                    </div>
                    <!-- End Checkbox -->

                    <!-- Checkbox -->
                    <div class="flex items-center">
                        <label for="hs-pro-shfloc-trousers" class="p-2 group w-full inline-flex items-center cursor-pointer text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                            <input type="checkbox" class="shrink-0 size-4.5 border-gray-300 rounded-sm text-blue-600 checked:border-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-500 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" id="hs-pro-shfloc-trousers">
                                <span class="ms-2 text-gray-800 dark:text-neutral-400">Filtro</span>
                                <span class="ms-auto text-xs text-gray-500 dark:text-neutral-500">(30)</span>
                        </label>
                    </div>
                    <!-- End Checkbox -->

                    <!-- Checkbox -->
                    <div class="flex items-center">
                        <label for="hs-pro-shfloc-jeans" class="p-2 group w-full inline-flex items-center cursor-pointer text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                            <input type="checkbox" class="shrink-0 size-4.5 border-gray-300 rounded-sm text-blue-600 checked:border-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-500 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" id="hs-pro-shfloc-jeans">
                                <span class="ms-2 text-gray-800 dark:text-neutral-400">Filtro</span>
                                <span class="ms-auto text-xs text-gray-500 dark:text-neutral-500">(21)</span>
                        </label>
                    </div>
                    <!-- End Checkbox -->

                    <!-- Checkbox -->
                    <div class="flex items-center">
                        <label for="hs-pro-shfloc-shorts" class="p-2 group w-full inline-flex items-center cursor-pointer text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                            <input type="checkbox" class="shrink-0 size-4.5 border-gray-300 rounded-sm text-blue-600 checked:border-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-500 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" id="hs-pro-shfloc-shorts">
                                <span class="ms-2 text-gray-800 dark:text-neutral-400">Filtro</span>
                                <span class="ms-auto text-xs text-gray-500 dark:text-neutral-500">(5)</span>
                        </label>
                    </div>
                    <!-- End Checkbox -->

                    <!-- Checkbox -->
                    <div class="flex items-center">
                        <label for="hs-pro-shfloc-jackets" class="p-2 group w-full inline-flex items-center cursor-pointer text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                            <input type="checkbox" class="shrink-0 size-4.5 border-gray-300 rounded-sm text-blue-600 checked:border-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-500 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" id="hs-pro-shfloc-jackets">
                                <span class="ms-2 text-gray-800 dark:text-neutral-400">Filtro</span>
                                <span class="ms-auto text-xs text-gray-500 dark:text-neutral-500">(15)</span>
                        </label>
                    </div>
                    <!-- End Checkbox -->
                </div>

                <div id="hs-pro-shfcc-heading" class="hs-collapse hidden w-full overflow-hidden transition-[height] duration-300" aria-labelledby="hs-pro-shfcc">
                    <div class="space-y-0.5">
                        <!-- Checkbox -->
                        <div class="flex items-center">
                            <label for="hs-pro-shfloc-sweaters" class="p-2 group w-full inline-flex items-center cursor-pointer text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                                <input type="checkbox" class="shrink-0 size-4.5 border-gray-300 rounded-sm text-blue-600 checked:border-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-500 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" id="hs-pro-shfloc-sweaters">
                                    <span class="ms-2 text-gray-800 dark:text-neutral-400">Filtro</span>
                                    <span class="ms-auto text-xs text-gray-500 dark:text-neutral-500">(44)</span>
                            </label>
                        </div>
                        <!-- End Checkbox -->

                        <!-- Checkbox -->
                        <div class="flex items-center">
                            <label for="hs-pro-shfloc-blazers" class="p-2 group w-full inline-flex items-center cursor-pointer text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                                <input type="checkbox" class="shrink-0 size-4.5 border-gray-300 rounded-sm text-blue-600 checked:border-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-500 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" id="hs-pro-shfloc-blazers">
                                    <span class="ms-2 text-gray-800 dark:text-neutral-400">Filtro</span>
                                    <span class="ms-auto text-xs text-gray-500 dark:text-neutral-500">(1)</span>
                            </label>
                        </div>
                        <!-- End Checkbox -->
                    </div>
                </div>
                <!-- End List -->

                <div class="mt-1">
                    <button type="button" class="hs-collapse-toggle inline-flex items-center gap-x-1.5 text-[13px] text-gray-800 underline underline-offset-4 hover:text-blue-600 focus:outline-hidden focus:text-blue-600 dark:text-neutral-200 dark:hover:text-blue-400 dark:focus:text-blue-400" id="hs-pro-shfcc" aria-expanded="false" aria-controls="hs-pro-shfcc-heading" data-hs-collapse="#hs-pro-shfcc-heading">
                        <span class="hs-collapse-open:hidden">Mostra altri</span>
                        <span class="hs-collapse-open:block hidden">Riduci</span>
                        <svg class="hs-collapse-open:rotate-180 shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                    </button>
                </div>
            </div>
            <!-- End Card -->

        </div>
    </div>
    <!-- End Body -->

    <!-- Footer -->
    <div class="p-6 border-t border-gray-200 dark:border-neutral-700">
        <div class="flex items-center gap-x-2">
            <button type="button" class="py-2 px-3 w-full inline-flex justify-center items-center gap-x-1.5 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-800 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 dark:border-neutral-700 dark:text-neutral-300" data-hs-overlay="#table-filters">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                Rimuovi filtri
            </button>

            <button type="button" class="py-2 px-3 w-full inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-blue-700" data-hs-overlay="#table-filters">
                Applica filtri
            </button>
        </div>
    </div>
    <!-- End Footer -->
</div>
<!-- End Table Filters Offcanvas -->

<script type="text/javascript">
$(function() {

    var start = moment().subtract(29, 'days');
    var end = moment();

    function cb(start, end) {
        $('#reportrange span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
    }

    $('#reportrange').daterangepicker({
        startDate: start,
        endDate: end,
        opens: 'left',
        ranges: {
           'Today': [moment(), moment()],
           'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
           'Last 7 Days': [moment().subtract(6, 'days'), moment()],
           'Last 30 Days': [moment().subtract(29, 'days'), moment()],
           'This Month': [moment().startOf('month'), moment().endOf('month')],
           'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
    }, cb);

    cb(start, end);

});
</script>

<script>
$(document).ready(function() {
    // Initialize Preline UI DataTable with JavaScript configuration
    const datatableConfig = {
        paging: true,
        pageLength: 10,
        searching: true,
        ordering: true,
        info: true,
        lengthChange: true,
        scrollCollapse: true,
        ajax: {
            url: '{{ routes("BE_USER_DATA") }}',
            type: 'GET',
            dataSrc: 'data'
        },
        select: {
            style: 'multi',
            selector: 'td:first-child'
        },
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        columnDefs: [
            {
                targets: 0,
                width: '50px',
                orderable: false,
                className: 'select-checkbox'
            },
            {
                targets: 4,
                render: function(data, type, row) {
                    if (type === 'display') {
                        const statusMap = {
                            'ACTIVE': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-500">Attivo</span>',
                            'INACTIVE': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-500">Inattivo</span>',
                            'PENDING': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-500">In Attesa</span>',
                            'customer': '<span class="inline-flex items-center gap-x-1.5 py-1.5 px-3 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800/30 dark:text-blue-500">Cliente</span>'
                        };
                        return statusMap[data] || data;
                    }
                    return data;
                }
            },
            {
                targets: -2,
                orderable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    if (type === 'display') {
                        return _renderActionDropdown(row);
                    }
                    return data;
                }
            },
            {
                targets: -1,
                className: 'control',
                orderable: false
            }
        ],
        language: {
            search: 'Cerca:',
            searchPlaceholder: 'Digita per cercare...',
            lengthMenu: 'Mostra _MENU_ elementi',
            paginate: {
                first: 'Primo',
                last: 'Ultimo',
                next: 'Successivo',
                previous: 'Precedente'
            },
            info: 'Mostra da _START_ a _END_ di _TOTAL_ elementi',
            infoEmpty: 'Mostra 0 a 0 di 0 elementi',
            infoFiltered: '(filtrati da _MAX_ elementi totali)',
            zeroRecords: 'Nessun record corrispondente trovato',
            emptyTable: 'Nessun dato disponibile nella tabella',
            loadingRecords: 'Caricamento...',
            processing: 'Elaborazione...'
        },
        pagingOptions: {
            pageBtnClasses: 'min-w-10 flex justify-center items-center text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 py-2.5 text-sm rounded-full disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:focus:bg-neutral-700 dark:hover:bg-neutral-700'
        },
        selecting: true,
        rowSelectingOptions: {
            selectAllSelector: '#hs-table-search-checkbox-all'
        }
    };

    // Set data-hs-datatable attribute on the table element
    const tableElement = document.querySelector('#user-datatable-container');
    tableElement.setAttribute('data-hs-datatable', JSON.stringify(datatableConfig));
    HSDataTable.autoInit();

    var table = HSDataTable.getInstance(document.querySelector("#user-datatable-container"), true).element;

    // Action dropdown renderer
    function _renderActionDropdown(row) {
        return `
            <div class="hs-dropdown relative inline-flex">
                <button id="hs-table-dropdown-${row.DT_RowId || Math.random()}" type="button" class="hs-dropdown-toggle py-1.5 px-2 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                    Azioni
                    <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                </button>
                <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden divide-y divide-gray-200 min-w-40 z-20 bg-white shadow-2xl rounded-lg p-2 mt-2 dark:divide-neutral-700 dark:bg-neutral-800 dark:border dark:border-neutral-700" role="menu" aria-orientation="vertical">
                    <div class="py-2 first:pt-0 last:pb-0">
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" href="#" onclick="_confirmSingleRow('${row.id}')">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 12l2 2 4-4"/><path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/><path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/></svg>
                            Conferma
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700" href="#" onclick="_archiveSingleRow('${row.id}')">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="5" x="2" y="3" rx="1"/><path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"/><path d="M10 12h4"/></svg>
                            Archivia
                        </a>
                        <a class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-red-600 hover:bg-red-100 focus:outline-hidden focus:bg-red-100 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30" href="#" onclick="_deleteSingleRow('${row.id}')">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                            Elimina
                        </a>
                    </div>
                </div>
            </div>
        `;
    }

    // Bulk action functions
    function _confirmSelectedRows() {
        const selectedRows = table.rows({ selected: true }).data();
        if (selectedRows.length === 0) {
            alert('Seleziona almeno un utente da confermare.');
            return;
        }

        if (confirm(`Sei sicuro di voler confermare ${selectedRows.length} utenti?`)) {
            const ids = [];
            selectedRows.each(function(row) {
                ids.push(row.id);
            });

            // Make AJAX call to confirm users
            $.ajax({
                url: '{{ routes("BE_USER_CONFIRM_BULK") }}',
                method: 'POST',
                data: { ids: ids },
                success: function(response) {
                    table.ajax.reload();
                    alert('Utenti confermati con successo.');
                },
                error: function() {
                    alert('Errore durante la conferma degli utenti.');
                }
            });
        }
    }

    function _archiveSelectedRows() {
        const selectedRows = table.rows({ selected: true }).data();
        if (selectedRows.length === 0) {
            alert('Seleziona almeno un utente da archiviare.');
            return;
        }

        if (confirm(`Sei sicuro di voler archiviare ${selectedRows.length} utenti?`)) {
            const ids = [];
            selectedRows.each(function(row) {
                ids.push(row.id);
            });

            // Make AJAX call to archive users
            $.ajax({
                url: '{{ routes("BE_USER_ARCHIVE_BULK") }}',
                method: 'POST',
                data: { ids: ids },
                success: function(response) {
                    table.ajax.reload();
                    alert('Utenti archiviati con successo.');
                },
                error: function() {
                    alert('Errore durante l\'archiviazione degli utenti.');
                }
            });
        }
    }

    function _deleteSelectedRows() {
        const selectedRows = table.rows({ selected: true }).data();
        if (selectedRows.length === 0) {
            alert('Seleziona almeno un utente da eliminare.');
            return;
        }

        if (confirm(`Sei sicuro di voler eliminare definitivamente ${selectedRows.length} utenti? Questa azione non può essere annullata.`)) {
            const ids = [];
            selectedRows.each(function(row) {
                ids.push(row.id);
            });

            // Make AJAX call to delete users
            $.ajax({
                url: '{{ routes("BE_USER_DELETE_BULK") }}',
                method: 'POST',
                data: { ids: ids },
                success: function(response) {
                    table.ajax.reload();
                    alert('Utenti eliminati con successo.');
                },
                error: function() {
                    alert('Errore durante l\'eliminazione degli utenti.');
                }
            });
        }
    }

    // Single row action functions
    window._confirmSingleRow = function(id) {
        if (confirm('Sei sicuro di voler confermare questo utente?')) {
            $.ajax({
                url: '{{ routes("BE_USER_CONFIRM") }}',
                method: 'POST',
                data: { id: id },
                success: function(response) {
                    table.ajax.reload();
                    alert('Utente confermato con successo.');
                },
                error: function() {
                    alert('Errore durante la conferma dell\'utente.');
                }
            });
        }
    };

    window._archiveSingleRow = function(id) {
        if (confirm('Sei sicuro di voler archiviare questo utente?')) {
            $.ajax({
                url: '{{ routes("BE_USER_ARCHIVE") }}',
                method: 'POST',
                data: { id: id },
                success: function(response) {
                    table.ajax.reload();
                    alert('Utente archiviato con successo.');
                },
                error: function() {
                    alert('Errore durante l\'archiviazione dell\'utente.');
                }
            });
        }
    };

    window._deleteSingleRow = function(id) {
        if (confirm('Sei sicuro di voler eliminare definitivamente questo utente? Questa azione non può essere annullata.')) {
            $.ajax({
                url: '{{ routes("BE_USER_DELETE") }}',
                method: 'POST',
                data: { id: id },
                success: function(response) {
                    table.ajax.reload();
                    alert('Utente eliminato con successo.');
                },
                error: function() {
                    alert('Errore durante l\'eliminazione dell\'utente.');
                }
            });
        }
    };

    // Enable/disable bulk action buttons based on selection
    table.on('select deselect', function() {
        const selectedCount = table.rows({ selected: true }).count();
        $('.actions-button').prop('disabled', selectedCount === 0);
    });

    // Initialize bulk action buttons as disabled
    $('.actions-button').prop('disabled', true);

    // Make bulk action functions available globally
    window._confirmSelectedRows = _confirmSelectedRows;
    window._archiveSelectedRows = _archiveSelectedRows;
    window._deleteSelectedRows = _deleteSelectedRows;
});
</script>

{% endblock %}
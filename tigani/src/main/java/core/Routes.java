package core;

import dao.BaseDao;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import utils.Defaults;

/**
 *
 * <AUTHOR>
 */
public class Routes {

    private static final Logger LOGGER = LoggerFactory.getLogger(Routes.class.getName());

    // errors
    public static final String ERROR_404 = "/404";
    
    // login   
    public static final String BE_LOGIN = "/login";
    public static final String BE_LOGIN_DO = "/login/do";
    public static final String BE_LOGOUT = "/logout";
    
    // forgot
    public static final String BE_FORGOT = "/forgot";      
    public static final String BE_FORGOT_DO = "/forgot/do";
    
    // dashboard
    public static final String BE_DASHBOARD = "/be/dashboard";
    
    // manteinance / tables
    public static final String BE_TABLE_COLLECTION = "/be/manteinance/tables";

    // settings
    /*public static final String BE_SETTINGS_LANGUAGES = "/be/settings/languages";
    public static final String BE_SETTINGS_SAVE = "/be/settings/save";*/

    public static final String BE_SETTINGS_COMPANY = "/be/settings/company";
    public static final String BE_SETTINGS_COMPANY_SAVE = "/be/settings/company/save";

    public static final String BE_SETTINGS_SMTP_COLLECTION = "/be/settings/smtps";
    public static final String BE_SETTINGS_SMTP = "/be/settings/smtp";
    public static final String BE_SETTINGS_SMTP_DATA = "/be/settings/smtp/data";
    public static final String BE_SETTINGS_SMTP_SAVE = "/be/settings/smtp/save";

    public static final String BE_SETTINGS_USER_COLLECTION = "/be/settings/users";
    public static final String BE_SETTINGS_USER = "/be/settings/user";
    public static final String BE_SETTINGS_USER_DATA = "/be/settings/user/data";
    public static final String BE_SETTINGS_USER_SAVE = "/be/settings/user/save";

    public static final String BE_SEND_MAIL = "/send/mail";

    // user
    public static final String BE_USER_COLLECTION = "/be/users";
    public static final String BE_USER = "/be/user";
    public static final String BE_USER_DATA = "/be/user/data";
    public static final String BE_USER_SAVE = "/be/user/save";
    public static final String BE_USER_OPERATE = "/be/user/operate";

    // insurance company
    public static final String BE_INSURANCECOMPANY_COLLECTION = "/be/insurancecompanies";
    public static final String BE_INSURANCECOMPANY = "/be/insurancecompany";
    public static final String BE_INSURANCECOMPANY_DATA = "/be/insurancecompany/data";
    public static final String BE_INSURANCECOMPANY_SAVE = "/be/insurancecompany/save";
    public static final String BE_INSURANCECOMPANY_OPERATE = "/be/insurancecompany/operate";

    // warranty
    public static final String BE_WARRANTY_COLLECTION = "/be/warranties";
    public static final String BE_WARRANTY = "/be/warranty";
    public static final String BE_WARRANTY_DATA = "/be/warranty/data";
    public static final String BE_WARRANTY_SAVE = "/be/warranty/save";
    public static final String BE_WARRANTY_OPERATE = "/be/warranty/operate";
    public static final String BE_WARRANTY_CRITERIA = "/be/warranty/criteria";

    // warranty criteria test
    public static final String BE_WARRANTY_CRITERIA_TEST = "/be/warranty/criteria-test";
    public static final String BE_WARRANTY_CRITERIA_SEARCH = "/be/warranty/criteria-search";

    // warranty details
    public static final String BE_WARRANTYDETAILS_COLLECTION = "/be/warrantydetails";
    public static final String BE_WARRANTYDETAILS = "/be/warrantydetail";
    public static final String BE_WARRANTYDETAILS_DATA = "/be/warrantydetail/data";
    public static final String BE_WARRANTYDETAILS_SAVE = "/be/warrantydetail/save";
    public static final String BE_WARRANTYDETAILS_OPERATE = "/be/warrantydetail/operate";

    // channel
    public static final String BE_CHANNEL_COLLECTION = "/be/channels";
    public static final String BE_CHANNEL = "/be/channel";
    public static final String BE_CHANNEL_DATA = "/be/channel/data";
    public static final String BE_CHANNEL_SAVE = "/be/channel/save";
    public static final String BE_CHANNEL_OPERATE = "/be/channel/operate";

    // warranty type
    public static final String BE_WARRANTYTYPE_COLLECTION = "/be/warrantytypes";
    public static final String BE_WARRANTYTYPE = "/be/warrantytype";
    public static final String BE_WARRANTYTYPE_DATA = "/be/warrantytype/data";
    public static final String BE_WARRANTYTYPE_SAVE = "/be/warrantytype/save";
    public static final String BE_WARRANTYTYPE_OPERATE = "/be/warrantytype/operate";

    // insurance provenance type
    public static final String BE_INSURANCEPROVENANCETYPE_COLLECTION = "/be/insuranceprovenancetypes";
    public static final String BE_INSURANCEPROVENANCETYPE = "/be/insuranceprovenancetype";
    public static final String BE_INSURANCEPROVENANCETYPE_DATA = "/be/insuranceprovenancetype/data";
    public static final String BE_INSURANCEPROVENANCETYPE_SAVE = "/be/insuranceprovenancetype/save";
    public static final String BE_INSURANCEPROVENANCETYPE_OPERATE = "/be/insuranceprovenancetype/operate";

    // field translation
    public static final String BE_FIELDTRANSLATION_COLLECTION = "/be/fieldtranslations";
    public static final String BE_FIELDTRANSLATION = "/be/fieldtranslation";
    public static final String BE_FIELDTRANSLATION_DATA = "/be/fieldtranslation/data";
    public static final String BE_FIELDTRANSLATION_SAVE = "/be/fieldtranslation/save";
    public static final String BE_FIELDTRANSLATION_OPERATE = "/be/fieldtranslation/operate";
    public static final String BE_FIELDTRANSLATION_REGENERATE = "/be/fieldtranslation/regenerate";

    // mailtemplate
    public static final String BE_MAILTEMPLATE_COLLECTION = "/be/mailtemplates";
    public static final String BE_MAILTEMPLATE = "/be/mailtemplate";
    public static final String BE_MAILTEMPLATE_DATA = "/be/mailtemplate/data";
    public static final String BE_MAILTEMPLATE_SAVE = "/be/mailtemplate/save";

    // import
    public static final String BE_IMPORT = "/be/import";
    public static final String BE_IMPORT_PROCESS = "/be/import/process";

    // files
    public static final String BE_IMAGE = "/be/image";
    public static final String BE_IMAGE_SAVE = "/be/image/save";
    public static final String BE_IMAGE_BLOB = "/be/image/blob";

    public static final String BE_FILE = "/be/file";

    public static Map<String, String> paths = new HashMap<>();
    public static Map<String, String> reversedPaths = new HashMap<>();

    public static Map<String, String> getPaths() {
        if (paths.isEmpty()) { // carico solo se bisogna
            reloadPaths();
        }

        return paths;
    }

    public static Map<String, String> getReversedPaths() {
        if (reversedPaths.isEmpty()) { // carico solo se bisogna
            reloadPaths();
        }

        return reversedPaths;
    }

    public static void reloadPaths() {
        paths = new HashMap<>();
        reversedPaths = new HashMap<>();
        try {
            Routes routes = new Routes();
            Field[] declaredFields = Routes.class.getDeclaredFields();
            for (Field field : declaredFields) {
                if (Modifier.isStatic(field.getModifiers())) {
                    paths.put(field.getName(), field.get(routes).toString());
                }
            }

            // caricamento da DB
            /*List<Path> databasePaths = BaseDao.getDocuments(Path.class);
            if (databasePaths != null && !databasePaths.isEmpty()) {
                for (Path path : databasePaths) {
                    if (path.getItems() != null && !path.getItems().isEmpty()) {
                        for (PathItem pathInLanguage : path.getItems()) {
                            if (Defaults.AVAILABLE_LANGUAGES.size() == 1) {
                                // niente prefisso della lingua
                                if (path.getItems().size() == 1) {
                                    paths.put(StringUtils.upperCase(path.getKey()), pathInLanguage.getDescription());
                                }
                            } else {
                                paths.put(StringUtils.upperCase(path.getKey() + "_" + pathInLanguage.getLanguage()), pathInLanguage.getDescription());
                            }
                        }
                    }
                }
            }*/

            for (String pathKey : paths.keySet()) {
                reversedPaths.put(paths.get(pathKey), pathKey);
            }

        } catch (IllegalAccessException | IllegalArgumentException | SecurityException ex) {
            LOGGER.error("Routes.getPaths(), can't populate paths. Error: " + ex.getMessage());
        } catch (Exception ex) {
            LOGGER.error("Routes.getPaths(), can't populate paths. Error: " + ex.getMessage());
        }
    }

    public static List<String> getLocalizedPath(String key) {
        Map<String, String> allPaths = getPaths();
        Map<String, String> allReversedPaths = getReversedPaths();

        List<String> localizedPaths = new ArrayList<>();
        if (Defaults.AVAILABLE_LANGUAGES.size() == 1) {
            localizedPaths.add(allPaths.get(allReversedPaths.get(key)));
        } else {
            boolean oneLanguageFound = false;
            for (String language : Defaults.AVAILABLE_LANGUAGES) {
                String localizedPath = allPaths.get(StringUtils.upperCase(key + "_" + language).replaceAll("/", ""));
                if (StringUtils.isNotBlank(localizedPath)) {
                    localizedPaths.add(localizedPath);
                    oneLanguageFound = true;
                }
            }

            if (!oneLanguageFound) {
                localizedPaths.add(allPaths.get(allReversedPaths.get(key)));
            }
        }

        return localizedPaths;
    }

    public static String getLocalizedPathByLanguage(String key, String language) {
        Map<String, String> allPaths = getPaths();
        Map<String, String> allReversedPaths = getReversedPaths();

        List<String> localizedPaths = new ArrayList<>();
        if (Defaults.AVAILABLE_LANGUAGES.size() == 1) {
            localizedPaths.add(allPaths.get(key));
        } else {
            List<String> languages = new ArrayList<>();
            if (StringUtils.isNotBlank(language)) {
                languages.add(language);
            } else {
                languages = Defaults.AVAILABLE_LANGUAGES;
            }

            boolean oneLanguageFound = false;
            for (String tmpLanguage : languages) {
                String localizedPath = allPaths.get(StringUtils.upperCase(key + "_" + tmpLanguage).replaceAll("/", ""));
                if (StringUtils.isNotBlank(localizedPath)) {
                    localizedPaths.add(localizedPath);
                    oneLanguageFound = true;
                }
            }

            if (!oneLanguageFound) {
                localizedPaths.add(allPaths.get(allReversedPaths.get(key)));
            }
        }

        if (!localizedPaths.isEmpty()) {
            return localizedPaths.get(0);
        } else {
            return Routes.ERROR_404;
        }
    }

//    public static Map<String, String> getPaths() {
//        if (paths.isEmpty()) { // carico solo se bisogna
//            try {
//                List<Path> pathsInDb = BaseDao.getDocuments(Path.class, Defaults.LANGUAGE);
//                for (Path path : pathsInDb) {
//                    paths.put(path.getKey(), path.getUrl());
//                }
//            } catch (Exception ex) {
//                LOGGER.error("Routes.getPaths(), can't populate paths. Error: " + ex.getMessage());
//            }
//        }
//
//        return paths;
//    }
}
